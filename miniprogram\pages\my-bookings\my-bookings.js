// my-bookings.js
// 我的预约页面逻辑文件
// 这是小程序的个人预约管理页面，负责展示用户的所有预约记录、筛选管理、取消预约等功能
// 类似于Web应用的订单管理页面或移动应用的预约历史页面

/**
 * 模块导入说明
 *
 * 这个页面是用户预约管理的核心页面，涉及：
 * 1. 数据展示：多种预约状态的分类展示
 * 2. 筛选功能：按状态、类型、时间等维度筛选
 * 3. 分页加载：支持大量预约记录的性能优化
 * 4. 预约操作：取消预约、查看详情等
 * 5. 实时更新：预约状态的实时同步
 *
 * 页面复杂度：
 * 这是项目中数据逻辑最复杂的页面之一，需要处理：
 * - 多维度数据筛选
 * - 分页懒加载
 * - 状态管理
 * - 权限控制
 */

// 导入Toast工具函数，用于用户反馈
import { showToast, showLoading, hideToast } from '../../utils/toast.js';

// 导入数据库查询函数
import { getUserBookings } from '../../utils/database.js';

// 导入预约相关业务逻辑函数
import { cancelBooking, loadSystemSettings } from '../../utils/bookingUtils.js';

/**
 * Page()函数：注册我的预约页面
 *
 * 页面功能：
 * 1. 预约记录展示：按状态分类显示所有预约
 * 2. 多维度筛选：按状态、类型、时间筛选预约
 * 3. 分页加载：支持大量预约记录的性能优化
 * 4. 预约管理：取消预约、查看详情、状态更新
 * 5. 实时同步：与数据库保持数据同步
 *
 * 数据架构设计：
 * 采用多层数据结构，支持复杂的筛选和分页需求：
 * 1. 原始数据层：从数据库获取的完整数据
 * 2. 分类数据层：按状态分类的数据
 * 3. 筛选数据层：按条件筛选后的数据
 * 4. 显示数据层：当前页面显示的数据（支持分页）
 */
Page({
  /**
   * data: 页面数据对象
   *
   * 数据结构说明：
   * 这个页面的数据结构比较复杂，因为需要支持：
   * - 多种预约状态的分类显示
   * - 多种筛选条件的组合
   * - 分页加载的性能优化
   * - 实时数据更新
   *
   * 数据分层设计：
   * Level 1: 原始数据（allBookings等）
   * Level 2: 筛选数据（filteredXXXBookings）
   * Level 3: 显示数据（visibleXXXBookings）
   * Level 4: UI状态（loading、flash等）
   */
  data: {
    /**
     * 顶部Tab切换相关数据
     *
     * 预约状态分类：
     * - all: 全部预约
     * - upcoming: 即将到来的预约
     * - cancelled: 已取消的预约
     * - completed: 已完成的预约
     */

    // 当前激活的顶部Tab：控制显示哪个分类的预约数据
    // 可选值：'all'(全部), 'upcoming'(已预约), 'cancelled'(已取消), 'completed'(已完成)
    // 默认值：'all' - 页面初始化时显示全部预约
    activeTab: 'all',

    // Tab选项卡数据配置
    // 使用与schedule页面相同的TDesign t-tabs组件
    // 数据驱动的设计模式，便于维护和扩展
    bookingTabs: [
      { label: '全部', value: 'all' },
      { label: '已预约', value: 'upcoming' },
      { label: '已取消', value: 'cancelled' },
      { label: '已完成', value: 'completed' }
    ],

    /**
     * 原始数据层
     *
     * 这些数组存储从数据库获取的原始预约数据
     * 按预约状态进行分类存储，便于后续处理
     *
     * 数据来源：loadBookings()方法从云数据库查询获得
     * 数据格式：每个元素都是预约对象，包含课程信息、时间、状态等
     */

    // 所有预约记录：包含用户的全部预约，不区分状态
    // 数据类型：Array<BookingObject>
    // 用途：作为其他分类数据的数据源
    allBookings: [],

    // 即将到来的预约：状态为'upcoming'或'ongoing'的预约
    // 数据类型：Array<BookingObject>
    // 筛选条件：booking.status === 'upcoming' || booking.status === 'ongoing'
    upcomingBookings: [],

    // 已取消的预约：用户主动取消或系统取消的预约
    // 数据类型：Array<BookingObject>
    // 筛选条件：booking.status === 'cancelled'
    cancelledBookings: [],

    // 已完成的预约：课程已结束的预约记录
    // 数据类型：Array<BookingObject>
    // 筛选条件：课程结束时间 < 当前时间 且 未被取消
    completedBookings: [],

    /**
     * 筛选数据层
     *
     * 这些数组存储经过筛选条件处理后的数据

     *
     * 数据流向：原始数据 → 筛选处理 → 筛选数据层
     * 处理方法：filterBookings()方法负责数据筛选
     */
    // 数据来源：allBookings经过activeFilter条件筛选
    filteredAllBookings: [],

    // 筛选后的即将到来预约：经过筛选的即将到来预约
    // 数据来源：upcomingBookings经过activeFilter条件筛选
    filteredUpcomingBookings: [],

    // 筛选后的已取消预约：经过筛选的已取消预约
    // 数据来源：cancelledBookings经过activeFilter条件筛选
    filteredCancelledBookings: [],

    // 筛选后的已完成预约：经过筛选的已完成预约
    // 数据来源：completedBookings经过activeFilter条件筛选
    filteredCompletedBookings: [],

    /**
     * 系统设置
     *
     * 从云数据库获取的系统配置信息
     * 影响预约业务逻辑的重要参数
     *
     * 数据来源：loadSystemSettings()方法从系统设置表获取
     * 更新时机：页面初始化时加载
     */

    // 取消预约时间限制（分钟）：课程开始前多长时间内不能取消预约
    // 数据类型：number | null
    // null表示尚未从数据库加载，避免使用可能不准确的默认值
    // 例如：180表示课程开始前3小时内不能取消预约
    // 业务逻辑：在cancelBooking()方法中用于判断是否允许取消
    cancelTimeLimit: null,





    // 当前激活的筛选条件：控制当前应用的筛选规则
    // 数据类型：string
    // 可选值：'all', 'group', 'private', 'venue'
    // 默认值：'all' - 初始状态显示所有类型
    // 更新时机：用户点击筛选Tab时更新
    // 影响范围：影响所有Tab页的数据显示
    activeFilter: 'all',

    /**
     * 复杂分页系统数据结构
     *
     * 这个页面采用了非常复杂的分页和数据管理策略：
     * 1. 按Tab分类管理数据（全部、已取消、已完成）
     * 2. 按时间维度分割数据（未来、历史）
     * 3. 双向分页加载（向上加载新数据、向下加载历史数据）
     * 4. 每个Tab都有独立的分页状态
     *
     * 设计原理：
     * - 性能优化：避免一次性加载大量数据
     * - 用户体验：支持无限滚动，数据按时间有序展示
     * - 内存管理：只保留可见区域的数据
     */

    /**
     * 全部预约Tab相关数据
     *
     * 这是"全部"Tab页的完整数据管理结构
     * 包含显示数据、分页状态、加载状态等
     */

    // 当前显示的全部预约卡片：用户在"全部"Tab中实际看到的预约列表
    // 数据类型：Array<BookingObject>
    // 数据来源：从allFutureBookings和allHistoryBookings中分页加载
    // 更新时机：页面初始化、用户滚动触发分页、筛选条件改变时
    // 渲染位置：页面的预约卡片列表区域
    visibleAllBookings: [],

    // 每页数据条数：控制分页加载的粒度
    // 数据类型：number
    // 默认值：5 - 平衡加载速度和用户体验
    // 业务考虑：太小会频繁加载，太大会影响首屏速度
    // 使用场景：所有分页加载方法都使用这个值
    pageSize: 5,

    // 顶部加载状态：控制向上滚动加载历史数据的状态
    // 数据类型：boolean
    // true：正在加载历史数据，显示loading动画，防止重复触发
    // false：未在加载，允许用户触发新的加载操作
    // 更新时机：开始加载时设为true，加载完成后设为false
    isLoadingTop: false,

    // 底部加载状态：控制向下滚动加载未来数据的状态
    // 数据类型：boolean
    // true：正在加载未来数据，显示loading动画，防止重复触发
    // false：未在加载，允许用户触发新的加载操作
    // 更新时机：开始加载时设为true，加载完成后设为false
    isLoadingBottom: false,

    // 新加载卡片的索引数组：控制新加载数据的滑入动画效果
    // 数据类型：Array<number>
    // 存储内容：新加载的预约记录在visibleAllBookings中的索引位置
    // 动画效果：新加载的卡片会有优雅的滑入提示
    // 清除时机：动画播放完成后（1秒后）清空数组
    flashIndexes: [],

    // 未来预约数据：存储今天及未来的所有预约记录
    // 数据类型：Array<BookingObject>
    // 时间范围：今天00:00:00 <= 预约时间
    // 排序规则：按开始时间正序排列，最近的预约在前面
    // 用途：向下滚动时从这个数组中分页加载数据
    // 数据来源：filterBookings()方法按时间筛选生成
    allFutureBookings: [],

    // 历史预约数据：存储今天之前的所有预约记录
    // 数据类型：Array<BookingObject>
    // 时间范围：预约时间 < 今天00:00:00
    // 排序规则：按开始时间倒序排列，最近的历史记录在前面
    // 用途：向上滚动时从这个数组中分页加载数据
    // 数据来源：filterBookings()方法按时间筛选生成
    allHistoryBookings: [],

    // 下拉刷新触发状态：控制下拉刷新操作的状态
    // 数据类型：boolean
    // true：用户正在执行下拉刷新，显示刷新动画
    // false：未在刷新状态，允许用户触发下拉刷新
    // 更新时机：用户下拉时设为true，刷新完成后设为false
    isRefresherTriggered: false,

    // 用户是否正在下拉：控制触顶提示的显示
    // 数据类型：boolean
    // true：用户正在下拉，显示"到顶啦！"提示
    // false：用户松手或未下拉，隐藏提示
    // 用途：实现只在下拉时显示提示，松手立即隐藏的效果
    isPullingDown: false,

    // 历史数据是否已全部加载：控制历史数据加载的边界状态
    // 数据类型：boolean
    // true：allHistoryBookings中的数据已全部加载到visibleAllBookings
    // false：还有历史数据未加载，用户可以继续向上滚动加载
    // 用途：控制"没有更多数据"提示的显示
    noMoreHistory: false,

    // 未来数据是否已全部加载：控制未来数据加载的边界状态
    // 数据类型：boolean
    // true：allFutureBookings中的数据已全部加载到visibleAllBookings
    // false：还有未来数据未加载，用户可以继续向下滚动加载
    // 用途：控制"没有更多数据"提示的显示
    noMoreFuture: false,

    /**
     * 已取消预约Tab相关数据
     *
     * 数据结构与全部预约Tab相同，但只包含已取消的预约
     * 每个Tab都有独立的分页状态和数据管理
     */

    // 当前显示的已取消预约卡片
    visibleCancelledBookings: [],

    // 重复定义，应该是代码错误，实际应该只有一个
    visibleCompletedBookings: [],

    // 已取消预约的顶部加载状态
    isLoadingTopCancelled: false,

    // 已取消预约的底部加载状态
    isLoadingBottomCancelled: false,

    // 已取消预约的历史数据是否已全部加载
    noMoreHistoryCancelled: false,

    // 已取消预约的未来数据是否已全部加载
    noMoreFutureCancelled: false,

    // 已取消预约的新加载卡片索引
    flashIndexesCancelled: [],

    // 已取消的未来预约数据
    cancelledFutureBookings: [],

    // 已取消的历史预约数据
    cancelledHistoryBookings: [],

    // 已取消预约的下拉刷新状态
    isRefresherTriggeredCancelled: false,

    // 已取消预约：用户是否正在下拉
    isPullingDownCancelled: false,

    /**
     * 已完成预约Tab相关数据
     *
     * 数据结构与其他Tab相同，但只包含已完成的预约
     */

    // 当前显示的已完成预约卡片（重复定义）
    visibleCompletedBookings: [],

    // 已完成预约的顶部加载状态
    isLoadingTopCompleted: false,

    // 已完成预约的底部加载状态
    isLoadingBottomCompleted: false,

    // 已完成预约的历史数据是否已全部加载
    noMoreHistoryCompleted: false,

    // 已完成预约的未来数据是否已全部加载
    noMoreFutureCompleted: false,

    // 已完成预约的新加载卡片索引
    flashIndexesCompleted: [],

    // 已完成的未来预约数据
    completedFutureBookings: [],

    // 已完成的历史预约数据
    completedHistoryBookings: [],

    // 已完成预约的下拉刷新状态
    isRefresherTriggeredCompleted: false,

    // 已完成预约：用户是否正在下拉
    isPullingDownCompleted: false,
  },

  /**
   * onLoad: 页面生命周期函数 - 页面加载时调用
   *
   * 主要任务：
   * 1. 隐藏TabBar（这是详情页面）
   * 2. 处理页面参数（支持直接跳转到特定Tab）
   * 3. 加载系统设置
   * 4. 初始化预约数据
   *
   * @param {Object} options - 页面参数对象
   *   - options.tab: 可选参数，指定要显示的Tab（'all', 'upcoming', 'cancelled', 'completed'）
   *
   * 参数处理：
   * 支持从其他页面直接跳转到特定Tab，提升用户体验
   * 例如：wx.navigateTo({ url: '/pages/my-bookings/my-bookings?tab=upcoming' })
   */
  onLoad(options) {
    // 隐藏TabBar导航栏
    // 我的预约页面通常从个人中心进入，不需要显示底部TabBar
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      this.getTabBar().setSelectedValue(-1);
    }

    // 处理页面参数，支持直接跳转到特定Tab
    // 使用逻辑与(&&)和逻辑或(||)进行安全的参数提取
    // options && options.tab：确保options存在且有tab属性
    // ? options.tab : 'all'：如果有tab参数则使用，否则默认为'all'
    const tab = options && options.tab ? options.tab : 'all';

    // 设置激活的Tab
    this.setData({ activeTab: tab });

    // 加载系统设置（取消预约时间限制等）
    this.loadSystemSettings();

    // 加载预约数据
    this.loadBookings();
  },

  /**
   * onShow: 页面生命周期函数 - 页面显示时调用
   *
   * 触发时机：
   * 1. 页面首次显示（在onLoad之后）
   * 2. 从其他页面返回到当前页面
   * 3. 从后台切换到前台
   *
   * 设计考虑：
   * - 每次显示都刷新数据，确保预约信息是最新的
   * - 重新隐藏TabBar，防止状态异常
   * - 重新加载系统设置，以防在其他页面被修改
   *
   * 数据同步：
   * 用户可能在课程详情页面进行预约或取消操作
   * 返回预约列表时需要看到最新的状态
   */
  onShow() {
    // 每次显示页面时都隐藏TabBar：确保页面UI状态正确
    // 防御性编程：先检查方法是否存在，再检查TabBar实例是否存在
    // typeof检查：确保getTabBar方法存在，避免调用undefined方法
    // 实例检查：确保TabBar组件已正确初始化
    if (typeof this.getTabBar === 'function' && this.getTabBar()) {
      // setSelectedValue(-1)：设置TabBar无选中状态
      // -1表示没有任何Tab被选中，适用于非TabBar页面
      this.getTabBar().setSelectedValue(-1);
    }

    // 重新加载系统设置：确保使用最新的系统配置
    // 调用时机：每次页面显示时
    // 必要性：系统设置可能在其他页面被管理员修改
    // 影响范围：取消时间限制等业务规则参数
    this.loadSystemSettings();

    // 重新加载预约数据：确保显示最新的预约状态和信息
    // 调用时机：每次页面显示时
    // 必要性：用户可能在其他页面进行了预约或取消操作
    // 数据同步：保证预约列表与数据库状态一致
    this.loadBookings();
  },

  /**
   * 下拉刷新功能（已移除）
   *
   * 设计变更说明：
   * 原本页面支持下拉刷新功能，但由于采用了复杂的分页系统，
   * 下拉刷新可能与分页加载产生冲突，因此被移除。
   *
   * 替代方案：
   * 用户可以通过切换Tab或重新进入页面来刷新数据
   *
   * 保留注释的原因：
   * 1. 记录设计决策的历史
   * 2. 如果将来需要恢复功能，可以参考原有实现
   * 3. 提醒开发者这个功能曾经存在但被有意移除
   */
  // 移除页面原有下拉刷新
  // async onPullDownRefresh() {
  //   await this.loadSystemSettings();
  //   await this.loadBookings();
  //   wx.stopPullDownRefresh();
  // },

  /**
   * switchTab: 切换标签页事件处理
   *
   * 功能说明：
   * 用户点击顶部Tab时，切换显示不同状态的预约记录
   *
   * @param {Object} e - 事件对象
   *   - e.currentTarget.dataset.tab: Tab标识符
   *
   * 切换流程：
   * 1. 获取目标Tab标识
   * 2. 更新activeTab状态
   * 3. 在状态更新完成后重新筛选数据
   *
   * setData回调函数：
   * setData的第二个参数是回调函数，在数据更新完成后执行
   * 这确保了筛选操作基于最新的activeTab值
   */
  switchTab(e) {
    // 从事件对象中获取Tab标识：通过dataset获取自定义数据属性
    // e.currentTarget：触发事件的元素（Tab按钮）
    // dataset.tab：HTML中data-tab属性的值，如'all', 'cancelled', 'completed'
    const tab = e.currentTarget.dataset.tab;

    // 更新激活的Tab状态：使用setData更新页面数据
    // setData是小程序的数据更新方法，类似于React的setState
    // 第一个参数：要更新的数据对象
    // 第二个参数：更新完成后的回调函数
    this.setData({
      activeTab: tab  // 更新当前激活的Tab标识
    }, () => {
      // 回调函数：在数据更新完成后执行
      // 为什么使用回调：确保activeTab已经更新，筛选操作基于最新值
      // 切换标签页后重新筛选数据，显示对应状态的预约
      this.filterBookings();
    });
  },

  /**
   * onTabChange: TDesign t-tabs组件的切换事件处理方法
   *
   * 功能说明：
   * 处理TDesign t-tabs组件的tab切换事件
   * 与schedule页面的onViewChange方法保持一致的命名和逻辑
   *
   * @param {Object} e - TDesign t-tabs组件的change事件对象
   *   - e.detail.value: 新选中的tab值
   *
   * 事件流程：
   * 1. 从事件对象中获取新的tab值
   * 2. 更新activeTab状态
   * 3. 重新筛选和显示对应的预约数据
   */
  onTabChange(e) {
    // 从TDesign t-tabs组件的事件对象中获取新的tab值
    // e.detail.value：TDesign组件标准的事件数据格式
    const activeTab = e.detail.value;

    // 更新当前激活的Tab状态
    this.setData({ activeTab }, () => {
      // 在状态更新完成后重新筛选数据
      // 确保筛选操作基于最新的activeTab值
      this.filterBookings();
    });
  },

  /**
   * loadSystemSettings: 加载系统设置的异步方法
   *
   * 功能说明：
   * 从云数据库加载系统配置，主要是取消预约的时间限制
   * 这个设置影响用户是否可以取消预约的业务逻辑
   *
   * 异步处理：
   * 使用async/await处理异步操作，确保设置加载完成后再继续
   *
   * 错误处理：
   * 使用try-catch捕获加载失败的情况，记录错误日志
   * 即使加载失败，也不影响页面的其他功能
   *
   * 日志记录：
   * 使用console.log记录关键操作，便于调试和问题排查
   */
  async loadSystemSettings() {
    try {
      // 记录开始加载的日志
      console.log('开始加载系统设置...');

      // 调用工具函数加载系统设置
      // loadSystemSettings函数会自动更新页面的cancelTimeLimit数据
      const cancelTimeLimit = await loadSystemSettings(this);

      // 记录加载成功的日志
      console.log('取消时间限制已设置:', cancelTimeLimit);
    } catch (error) {
      // 错误处理：记录错误日志
      // 不向用户显示错误提示，因为这不是用户直接操作导致的
      console.error('加载系统设置失败:', error);
    }
  },

  /**
   * loadBookings: 加载预约数据的核心方法
   *
   * 这是页面最重要的方法之一，负责：
   * 1. 获取用户的所有预约记录
   * 2. 关联查询课程详情和用户信息
   * 3. 按状态分类预约数据
   * 4. 按时间维度分割数据
   * 5. 更新页面显示
   *
   * 数据处理流程：
   * 原始预约数据 → 关联课程信息 → 状态计算 → 分类存储 → 时间分割 → 页面更新
   *
   * 性能考虑：
   * - 批量查询课程信息，减少网络请求
   * - 使用Map数据结构快速查找
   * - 客户端数据处理，减少服务器压力
   */
  async loadBookings() {
    try {
      // 显示加载提示，提升用户体验
      showLoading(this, '加载中...');

      /**
       * 用户身份验证
       *
       * 安全检查：
       * 确保用户已登录且有有效的openid
       * 这是数据安全的第一道防线
       */
      const app = getApp();
      const userInfo = app.getUserInfo();

      // 检查用户登录状态
      if (!userInfo || !userInfo.openid) {
        // 用户未登录，显示提示并返回
        showToast(this, { message: '请先登录', theme: 'warning' });
        hideToast(this);
        return;
      }

      // 获取用户ID（微信openid）
      const userId = userInfo.openid;

      /**
       * 获取用户预约数据
       *
       * 数据来源：
       * 从云数据库的bookings表中查询当前用户的所有预约记录
       */
      const bookings = await getUserBookings(userId);

      /**
       * 初始化数据分类容器
       *
       * 分类策略：
       * 按预约状态将数据分为四类，便于后续处理和显示
       */
      const all = [];        // 所有预约（除了已删除的）
      const upcoming = [];   // 即将到来的预约
      const cancelled = [];  // 已取消的预约
      const completed = [];  // 已完成的预约

      /**
       * 提取课程ID列表
       *
       * 数据关联准备：
       * 从预约记录中提取所有课程ID，用于批量查询课程详情
       *
       * Array.map()：将预约数组转换为课程ID数组
       * Array.filter()：过滤掉空的课程ID
       * !!id：双重否定，将值转换为布尔值，过滤掉null、undefined、空字符串等
       */
      const courseIds = bookings.map(b => b.courseId).filter(id => !!id);

      /**
       * 初始化关联数据映射表
       *
       * Map vs Object：
       * 这里使用普通对象而不是Map，因为：
       * 1. 键都是字符串类型
       * 2. 需要与现有代码保持兼容
       * 3. 对象字面量语法更简洁
       */
      let courseMap = {};    // 课程ID → 课程详情的映射表
      let userMap = {};      // 用户ID → 用户信息的映射表

      /**
       * 批量获取关联数据
       *
       * 数据关联策略：
       * 1. 批量获取课程详情：避免单个查询的性能问题
       * 2. 提取讲师信息：从课程中提取所有讲师的openid
       * 3. 批量获取用户信息：获取讲师的昵称等基础信息
       *
       * 性能优化：
       * - 使用Map数据结构快速查找
       * - 批量查询减少网络请求
       * - 去重处理避免重复查询
       */
      if (courseIds.length) {
        try {
          const db = wx.cloud.database();
          const _ = db.command;

          /**
           * 第一步：批量获取课程详情
           *
           * 查询策略：
           * 使用 _.in() 查询多个课程ID对应的课程信息
           * 这比逐个查询效率高很多
           */
          const courseRes = await db.collection('courses').where({
            _id: _.in(courseIds)
          }).get();

          /**
           * 构建课程映射表
           *
           * Array.reduce() 用法：
           * 将数组转换为对象，以课程ID为键，课程信息为值
           *
           * 等价于：
           * courseMap = {};
           * courseRes.data.forEach(c => {
           *   courseMap[c._id] = c;
           * });
           *
           * 但reduce写法更简洁
           */
          courseMap = courseRes.data.reduce((acc, c) => {
            acc[c._id] = c;
            return acc;
          }, {});

          /**
           * 第二步：提取所有讲师的openid
           *
           * 数据结构说明：
           * course.coach 是一个openid数组，如：["openid1", "openid2"]
           * 需要将所有课程的讲师openid收集到一个数组中
           */
          const allCoachOpenids = [];
          courseRes.data.forEach(course => {
            // 检查讲师字段是否存在且为数组
            if (course.coach && Array.isArray(course.coach)) {
              // 使用扩展运算符将讲师数组展开并添加到总数组中
              allCoachOpenids.push(...course.coach);
            }
          });

          /**
           * 去重处理
           *
           * 问题：
           * 同一个讲师可能教授多门课程，会出现重复的openid
           *
           * 解决方案：
           * 使用Set数据结构自动去重，然后转换回数组
           *
           * 技术细节：
           * new Set(array)：创建Set，自动去重
           * [...set]：使用扩展运算符将Set转换回数组
           */
          const uniqueCoachOpenids = [...new Set(allCoachOpenids)];

          /**
           * 第三步：批量获取讲师用户信息
           *
           * 只有当有讲师时才查询用户信息
           * 避免无意义的数据库查询
           */
          if (uniqueCoachOpenids.length > 0) {
            try {
              // 批量查询讲师的用户基础信息
              const userRes = await db.collection('users').where({
                openid: _.in(uniqueCoachOpenids)
              }).get();

              /**
               * 构建用户映射表
               *
               * 将用户数组转换为以openid为键的对象
               * 便于后续快速查找用户信息
               */
              userMap = userRes.data.reduce((acc, user) => {
                acc[user.openid] = user;
                return acc;
              }, {});
            } catch (userErr) {
              // 用户信息查询失败不影响主流程
              // 只是讲师姓名会显示为openid后4位
              console.warn('批量获取讲师用户信息失败:', userErr);
            }
          }
        } catch (err) {
          // 课程详情查询失败的处理
          // 记录警告但不中断流程，预约列表仍可显示基础信息
          console.warn('批量获取课程详情失败:', err);
        }
      }
      /**
       * 预约数据处理和状态计算
       *
       * 这是整个方法的核心部分，负责：
       * 1. 合并预约数据和课程详情
       * 2. 计算预约的实际状态
       * 3. 格式化显示数据
       * 4. 判断操作权限（如是否可取消）
       *
       * 数据处理复杂度：
       * 每个预约记录需要结合课程信息、时间状态、用户权限等多个维度
       * 进行综合计算，得出最终的显示状态和可用操作
       */

      // 定义即将到来的状态（暂时未使用，保留用于扩展）
      const upcomingStatuses = ['upcoming', 'ongoing'];

      /**
       * 遍历处理每个预约记录
       *
       * 处理流程：
       * 1. 获取关联的课程详情
       * 2. 格式化时间显示
       * 3. 处理讲师信息
       * 4. 计算预约状态
       * 5. 判断操作权限
       * 6. 分类存储数据
       */
      bookings.forEach(booking => {
        /**
         * 获取课程详情
         *
         * 使用 || {} 提供默认值：
         * 如果找不到对应的课程详情，使用空对象作为默认值
         * 避免后续访问属性时出现undefined错误
         */
        const courseDetail = courseMap[booking.courseId] || {};

        /**
         * 时间数据处理
         *
         * 时间格式转换：
         * 数据库中的时间可能是字符串或时间戳格式
         * 统一转换为Date对象进行处理
         *
         * 默认值处理：
         * 如果课程没有时间信息，使用当前时间作为默认值
         * 避免时间计算出错
         */
        const startTime = courseDetail.startTime ? new Date(courseDetail.startTime) : new Date();
        const endTime = courseDetail.endTime ? new Date(courseDetail.endTime) : new Date();

        /**
         * 时间显示格式化
         *
         * 格式：HH:MM-HH:MM
         * 例如：14:30-16:00
         *
         * formatTime方法：
         * 将Date对象格式化为HH:MM格式的字符串
         */
        const timeStr = `${this.formatTime(startTime)}-${this.formatTime(endTime)}`;

        /**
         * 讲师信息处理
         *
         * 数据结构：
         * courseDetail.coach 是讲师openid数组
         * 需要转换为讲师姓名数组用于显示
         *
         * 处理逻辑：
         * 1. 检查讲师数据是否存在且为数组
         * 2. 遍历openid数组，查找对应的用户信息
         * 3. 优先使用昵称，没有则使用openid后4位
         */
        let coachNames = [];
        if (courseDetail.coach && Array.isArray(courseDetail.coach) && courseDetail.coach.length > 0) {
          coachNames = courseDetail.coach.map(openid => {
            // 在用户映射表中查找讲师信息
            const user = userMap[openid];

            // 返回昵称或openid后4位作为备用显示
            return user ? user.nickName : openid.slice(-4);
          });
        }

        /**
         * 预约状态计算
         *
         * 状态计算逻辑：
         * 1. 基础状态：从预约记录中获取原始状态
         * 2. 时间状态：根据当前时间和课程时间计算实际状态
         * 3. 最终状态：综合考虑得出用户看到的状态
         *
         * 状态类型：
         * - upcoming: 即将到来（课程还未开始）
         * - ongoing: 进行中（课程正在进行）
         * - completed: 已完成（课程已结束）
         * - cancelled: 已取消（用户主动取消）
         */
        let status = booking.status;  // 获取原始状态
        const now = new Date();       // 当前时间
        let isCompleted = false;      // 是否已完成标识

        /**
         * 基于时间的状态自动更新
         *
         * 业务规则：
         * 如果课程已经结束，且预约未被取消，则自动标记为已完成
         * 这样用户可以看到历史课程的完成状态
         */
        if (courseDetail.endTime) {
          const courseEndTime = new Date(courseDetail.endTime);

          // 判断课程是否已结束
          if (now >= courseEndTime) {
            if (booking.status !== 'cancelled') {
              // 课程已结束且未取消，标记为已完成
              status = 'completed';
              isCompleted = true;
            } else {
              // 课程已结束但已取消，保持取消状态
              status = 'cancelled';
            }
          }
        }

        /**
         * 取消权限判断
         *
         * 取消规则：
         * 1. 只有即将到来的预约可以取消
         * 2. 必须在取消时间限制之前
         * 3. 已取消或已完成的预约不能再次取消
         *
         * 时间限制：
         * cancelTimeLimit 是系统设置的取消时间限制（分钟）
         * 例如：180分钟表示课程开始前3小时内不能取消
         */
        let canCancel = false;
        if (status === 'upcoming' && courseDetail.startTime) {
          const courseStartTime = new Date(courseDetail.startTime);
          if (now < courseStartTime) {
            // 检查取消时间限制
            const cancelTimeLimit = this.data.cancelTimeLimit;
            if (cancelTimeLimit !== null && cancelTimeLimit !== undefined) {
              const timeDiffMinutes = (courseStartTime.getTime() - now.getTime()) / (1000 * 60);
              if (timeDiffMinutes >= cancelTimeLimit) {
                canCancel = true;
              }
            } else {
              // 如果系统设置未加载，默认允许取消
              canCancel = true;
            }
          }
        }
        const courseData = {
          id: booking._id,
          courseId: booking.courseId,
          courseName: courseDetail.name || '未知课程',
          date: this.formatDate(startTime),
          time: timeStr,
          coach: this.formatCoachName(coachNames),
          venue: courseDetail.venue || '未知地点',
          type: courseDetail.type || '未知类型',
          status: status,
          statusText: this.getStatusText(status),
          canCancel: canCancel,
          // 是否可以再次预约
          // 只有已完成或已取消的课程才能再次预约
          canBookAgain: status === 'completed' || status === 'cancelled',

          // 课程开始时间（用于排序和时间判断）
          // 确保startTime是Date对象，用于后续的时间比较
          startTime: courseDetail.startTime ? new Date(courseDetail.startTime) : new Date()
        };

        // 将处理后的数据添加到全部预约列表
        all.push(courseData);

        /**
         * 预约分类逻辑
         *
         * 分类规则：
         * 1. upcoming（即将到来）：预约状态为upcoming或ongoing，且课程未结束，且未取消
         * 2. cancelled（已取消）：预约状态为cancelled，或计算后的状态为cancelled
         * 3. completed（已完成）：课程已结束且未取消，或预约状态为completed
         *
         * 注意：一个预约可能同时属于多个分类（如已取消的历史课程）
         */

        // 即将到来的预约分类
        if ((['upcoming', 'ongoing'].includes(booking.status)) && endTime > now && booking.status !== 'cancelled') {
          upcoming.push(courseData);
        }

        // 已取消的预约分类
        if (booking.status === 'cancelled' || status === 'cancelled') {
          cancelled.push(courseData);
        }

        // 已完成的预约分类
        // 条件：(课程已结束且未取消) 或 (预约状态为completed)
        if ((courseDetail.endTime && now >= new Date(courseDetail.endTime) && booking.status !== 'cancelled') || booking.status === 'completed') {
          completed.push(courseData);
        }
      });

      /**
       * 数据排序函数
       *
       * 排序规则：按课程开始时间升序排列
       * 最早的课程排在前面，最晚的课程排在后面
       *
       * 排序算法：
       * 使用Array.sort()方法，传入比较函数
       * 比较函数返回值：
       * - 负数：a排在b前面
       * - 0：a和b位置不变
       * - 正数：a排在b后面
       */
      const sortByTime = (arr) => arr.sort((a, b) => {
        // 获取课程A和课程B的详情
        const courseA = courseMap[a.courseId] || {};
        const courseB = courseMap[b.courseId] || {};

        // 获取开始时间的时间戳，没有时间则使用0
        const timeA = courseA.startTime ? new Date(courseA.startTime).getTime() : 0;
        const timeB = courseB.startTime ? new Date(courseB.startTime).getTime() : 0;

        // 返回时间差，实现升序排列
        return timeA - timeB;
      });

      // 对所有分类的数据进行排序
      sortByTime(all);
      sortByTime(upcoming);
      sortByTime(cancelled);
      sortByTime(completed);

      /**
       * 更新页面数据
       *
       * setData的回调函数：
       * 在数据更新完成后调用filterBookings()方法
       * 确保筛选操作基于最新的数据
       */
      this.setData({
        allBookings: all,           // 全部预约
        upcomingBookings: upcoming, // 即将到来的预约
        cancelledBookings: cancelled, // 已取消的预约
        completedBookings: completed  // 已完成的预约
      }, () => {
        // 回调函数：数据更新完成后执行筛选
        this.filterBookings();
      });

      // 隐藏加载提示
      hideToast(this);
    } catch (error) {
      /**
       * 错误处理
       *
       * 处理策略：
       * 1. 记录错误日志
       * 2. 隐藏加载提示
       * 3. 显示用户友好的错误信息
       */
      console.error('加载预约数据失败:', error);
      hideToast(this);
      showToast(this, { message: '加载失败', theme: 'error' });
    }
  },

  /**
   * formatTime: 格式化时间显示的工具方法
   *
   * 功能说明：
   * 将Date对象格式化为HH:MM格式的时间字符串
   * 用于在页面上显示课程的开始时间和结束时间
   *
   * @param {Date} date - 要格式化的Date对象
   * @returns {string} - 格式化后的时间字符串，如"14:30"
   *
   * 格式化规则：
   * - 小时：24小时制，不足两位前面补0
   * - 分钟：不足两位前面补0
   * - 分隔符：使用冒号(:)
   *
   * 技术细节：
   * padStart(2, '0')：字符串填充方法
   * - 第一个参数：目标长度
   * - 第二个参数：填充字符
   * - 如果字符串长度不足，在前面补充指定字符
   *
   * 示例：
   * - 上午9点5分 → "09:05"
   * - 下午2点30分 → "14:30"
   * - 晚上11点0分 → "23:00"
   */
  formatTime(date) {
    // 获取小时数并格式化为两位数字
    const hours = date.getHours().toString().padStart(2, '0');

    // 获取分钟数并格式化为两位数字
    const minutes = date.getMinutes().toString().padStart(2, '0');

    // 返回HH:MM格式的时间字符串
    return `${hours}:${minutes}`;
  },

  // 格式化日期
  formatDate(date) {
    const weekdays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
    const year = date.getFullYear();
    const month = date.getMonth() + 1;
    const day = date.getDate();
    const weekday = weekdays[date.getDay()];
      return `${year}年${month}月${day}日（${weekday}）`;
  },

  /**
   * getStatusText: 获取状态显示文本的工具方法
   *
   * 功能说明：
   * 将预约状态代码转换为用户友好的中文显示文本
   * 提供统一的状态文本映射
   *
   * @param {string} status - 预约状态代码
   * @returns {string} - 对应的中文显示文本
   *
   * 状态映射：
   * - 'upcoming': '即将开始' - 课程还未开始
   * - 'ongoing': '进行中' - 课程正在进行
   * - 'completed': '已完成' - 课程已经结束
   * - 'cancelled': '已取消' - 预约已被取消
   * - 其他: '未知状态' - 异常状态的默认显示
   *
   * 设计模式：
   * 使用映射表模式，便于维护和扩展状态类型
   */
  getStatusText(status) {
    // 状态映射表
    const statusMap = {
      'upcoming': '即将开始',
      'ongoing': '进行中',
      'completed': '已完成',
      'cancelled': '已取消'
    };

    // 返回对应的中文文本，未找到则返回默认值
    return statusMap[status] || '未知状态';
  },

  /**
   * formatCoachName: 格式化讲师姓名显示的工具方法
   *
   * 功能说明：
   * 处理讲师姓名的显示格式，支持单个讲师和多个讲师的情况
   * 确保在各种数据格式下都能正确显示讲师信息
   *
   * @param {string|Array|null|undefined} coachName - 讲师姓名数据
   * @returns {string} - 格式化后的讲师姓名字符串
   *
   * 处理场景：
   * 1. 空值处理：null、undefined、空字符串
   * 2. 数组处理：多个讲师的姓名数组
   * 3. 字符串处理：单个讲师的姓名字符串
   * 4. 异常处理：其他类型的数据
   *
   * 显示规则：
   * - 单个讲师：直接显示姓名
   * - 多个讲师：用中文顿号(、)连接
   * - 空数据：显示"未知讲师"
   */
  formatCoachName(coachName) {
    // 处理空值情况
    if (!coachName) {
      return '未知讲师';
    }

    // 数组类型处理（多个讲师）
    if (Array.isArray(coachName)) {
      if (coachName.length === 0) {
        return '未知讲师';
      }
      // 过滤有效姓名并用顿号连接
      return coachName.filter(name => name && name.trim()).join('、');
    }

    // 字符串类型处理（单个讲师）
    if (typeof coachName === 'string') {
      return coachName.trim() || '未知讲师';
    }

    // 其他类型的数据，返回默认值
    return '未知讲师';
  },

  /**
   * cancelBooking: 取消预约的异步方法
   *
   * 功能说明：
   * 处理用户取消预约的操作
   * 调用统一的取消预约工具函数，确保业务逻辑一致性
   *
   * @param {Object} e - 事件对象
   *   - e.currentTarget.dataset.booking: 预约数据对象
   *
   * 数据适配：
   * 将预约数据转换为课程对象格式，以适配统一的取消预约接口
   * 这种设计模式叫做适配器模式（Adapter Pattern）
   *
   * 回调处理：
   * - 成功回调：重新加载预约数据，确保列表状态一致
   * - 错误回调：记录错误日志，便于问题排查
   */
  async cancelBooking(e) {
    // 从事件对象中获取预约数据
    const booking = e.currentTarget.dataset.booking;

    /**
     * 数据适配：构造课程对象
     *
     * 适配原因：
     * cancelBooking工具函数期望接收课程对象格式的数据
     * 但这里的数据是预约对象格式，需要进行转换
     *
     * 字段映射：
     * - id: 课程ID（用于标识课程）
     * - name: 课程名称（用于显示）
     * - startTime: 开始时间（用于时间验证）
     * - ended: 是否已结束（用于状态判断）
     * - started: 是否已开始（用于状态判断）
     */
    const course = {
      id: booking.courseId,                    // 课程ID
      name: booking.courseName,                // 课程名称
      startTime: booking.startTime,            // 开始时间
      ended: booking.status === 'completed',   // 是否已结束
      started: booking.status === 'ongoing'    // 是否已开始
    };

    /**
     * 调用统一的取消预约工具函数
     *
     * 参数说明：
     * 1. course: 适配后的课程对象
     * 2. this: 当前页面实例，用于显示提示信息
     * 3. 成功回调函数
     * 4. 错误回调函数
     *
     * 异步处理：
     * 使用await等待取消操作完成
     * 确保后续操作基于最新状态
     */
    await cancelBooking(course, this,
      // 成功回调函数
      (course) => {
        /**
         * 取消成功后的处理
         *
         * 重新加载预约数据：
         * 确保页面显示的预约列表与数据库状态一致
         * 用户可以立即看到取消后的状态变化
         */
        this.loadBookings();
      },
      // 错误回调函数
      (error) => {
        /**
         * 取消失败后的处理
         *
         * 错误日志记录：
         * 记录详细的错误信息，便于问题排查和调试
         * 用户界面的错误提示由工具函数内部处理
         */
        console.error('取消预约失败:', error);
      }
    );
  },

  /**
   * viewCourseDetail: 查看课程详情的事件处理
   *
   * 功能说明：
   * 用户点击预约卡片时，跳转到课程详情页面
   * 让用户可以查看课程的完整信息
   *
   * @param {Object} e - 事件对象
   *   - e.currentTarget.dataset.course: 课程数据对象
   *
   * 页面跳转：
   * 使用wx.navigateTo进行页面跳转
   * 通过URL参数传递课程ID，详情页面根据ID加载数据
   *
   * 用户体验：
   * - 用户可以从预约列表直接查看课程详情
   * - 详情页面提供更丰富的课程信息
   * - 支持返回操作，用户可以回到预约列表
   */
  viewCourseDetail(e) {
    // 从事件对象中获取课程数据
    const course = e.currentTarget.dataset.course;

    /**
     * 页面跳转
     *
     * 跳转方式：wx.navigateTo
     * - 保留当前页面，新页面入栈
     * - 用户可以通过返回按钮回到当前页面
     * - 适合查看详情的场景
     *
     * URL参数传递：
     * 通过查询字符串传递课程ID
     * 格式：/pages/course-detail/course-detail?id=课程ID
     *
     * 目标页面处理：
     * 课程详情页面在onLoad方法中接收id参数
     * 根据ID从数据库加载完整的课程信息
     */
    wx.navigateTo({
      url: `/pages/course-detail/course-detail?id=${course.courseId}`
    });
  },

  /**
   * filterBookings: 筛选预约数据的核心方法
   *
   * 功能说明：
   * 根据用户选择的筛选条件对预约数据进行筛选和分类
   * 这是整个预约列表页面的数据处理核心
   *
   * 处理流程：
   * 1. 
   * 2. 按时间维度分类（今天及未来 vs 历史）
   * 3. 按状态维度分类（全部、已取消、已完成）
   * 4. 实现分页显示逻辑
   * 5. 更新页面显示状态
   *
   * 复杂度说明：
   * 这个方法处理多维度的数据筛选和分页，是页面中最复杂的业务逻辑之一
   */
  filterBookings() {
    // 调试日志：记录方法调用
    console.log('filterBookings called');

    /**
     * 获取页面数据
     *
     * 解构赋值获取需要的数据：
     * - activeFilter: 当前激活的筛选条件
     * - allBookings: 全部预约数据
     * - upcomingBookings: 即将到来的预约
     * - cancelledBookings: 已取消的预约
     * - completedBookings: 已完成的预约
     * - pageSize: 分页大小
     */
    const { activeFilter, allBookings, upcomingBookings, cancelledBookings, completedBookings, pageSize } = this.data;

    /**
     * 定义过滤函数
     *
     * 根据activeFilter筛选预约记录
     * 目前暂时返回true，显示所有预约
     * 后续可以根据需要添加具体的筛选逻辑
     */
    const filterBooking = (booking) => {
      // 暂时返回true，显示所有预约
      // 后续可以根据activeFilter添加具体筛选逻辑
      return true;
    };

    /**
     * 调试代码：时间数据调试
     *
     * 目的：
     * 调试预约数据中的时间字段格式和类型
     * 帮助排查时间相关的显示和排序问题
     *
     * 调试信息：
     * - id: 预约ID
     * - startTime: 开始时间原始值
     * - typeof: 数据类型
     * - toString: 字符串表示
     * - json: JSON序列化结果
     */
    try {
      allBookings.forEach(b => {
        console.log('startTime调试单条', {
          id: b.id,
          startTime: b.startTime,
          typeof: typeof b.startTime,
          toString: b.startTime && b.startTime.toString ? b.startTime.toString() : '',
          json: JSON.stringify(b.startTime)
        });
      });
    } catch (e) {
      console.error('startTime调试异常', e);
    }

    /**
     * 应用课程类型筛选
     *
     * 使用Array.filter()方法筛选数据
     * 只保留符合筛选条件的预约记录
     */
    const filteredAll = allBookings.filter(filterBooking);

    /**
     * 时间维度分类准备
     *
     * 将筛选后的数据按时间维度进行分类：
     * - 今天及未来：包括今天和未来的预约
     * - 历史：过去的预约记录
     */
    const now = new Date();

    // 生成今天的日期字符串（YYYY-MM-DD格式）
    // 注意：这个变量在当前版本中未使用，但保留用于可能的扩展
    const todayStr = now.getFullYear() + '-' + (now.getMonth()+1).toString().padStart(2,'0') + '-' + now.getDate().toString().padStart(2,'0');

    /**
     * 日期比较工具函数
     *
     * 功能说明：
     * 判断给定日期是否为今天或今天之后
     * 用于将预约数据分类为"今天及未来"和"历史"
     *
     * @param {Date} d - 要比较的日期
     * @param {Date} now - 当前日期
     * @returns {boolean} - 是否为今天或今天之后
     *
     * 比较逻辑：
     * 1. 年份比较：如果年份大于当前年份，返回true
     * 2. 月份比较：同年且月份大于当前月份，返回true
     * 3. 日期比较：同年同月且日期大于等于当前日期，返回true
     * 4. 其他情况返回false
     *
     * 注意：
     * 这里使用的是日期级别的比较，不考虑具体的时分秒
     * 即今天的任何时间都被认为是"今天及未来"
     */
    const isSameOrAfterToday = (d, now) => {
      // 输入验证：确保传入的是Date对象
      if (!(d instanceof Date)) return false;

      // 多级比较逻辑
      return (
        // 年份大于当前年份
        d.getFullYear() > now.getFullYear() ||
        // 同年且月份大于当前月份
        (d.getFullYear() === now.getFullYear() && d.getMonth() > now.getMonth()) ||
        // 同年同月且日期大于等于当前日期
        (d.getFullYear() === now.getFullYear() && d.getMonth() === now.getMonth() && d.getDate() >= now.getDate())
      );
    };
    const isBeforeToday = (d, now) => {
      if (!(d instanceof Date)) return false;
      return (
        d.getFullYear() < now.getFullYear() ||
        (d.getFullYear() === now.getFullYear() && d.getMonth() < now.getMonth()) ||
        (d.getFullYear() === now.getFullYear() && d.getMonth() === now.getMonth() && d.getDate() < now.getDate())
      );
    };
    const allFutureBookings = filteredAll.filter(b => isSameOrAfterToday(b.startTime, now));
    const allHistoryBookings = filteredAll.filter(b => isBeforeToday(b.startTime, now));

    // 修正：全部tab初始显示最近的数据（优先未来，没有则显示最近历史）
    let visibleAllBookings = [];
    if (allFutureBookings.length >= pageSize) {
      // 未来预约足够，显示未来的前5条
      visibleAllBookings = allFutureBookings.slice(0, pageSize);
    } else if (allFutureBookings.length > 0) {
      // 未来预约不足5条，补充历史预约
      const need = pageSize - allFutureBookings.length;
      const historyPart = allHistoryBookings.slice(-need);
      // 合并并按时间排序：历史预约在前，未来预约在后
      visibleAllBookings = historyPart.concat(allFutureBookings);
    } else {
      // 只有历史预约，显示最近的5条历史预约
      visibleAllBookings = allHistoryBookings.slice(-pageSize);
    }
    // 已取消tab分页分组
    const filteredCancelled = cancelledBookings.filter(filterBooking);
    const cancelledFutureBookings = filteredCancelled.filter(b => isSameOrAfterToday(b.startTime, now));
    const cancelledHistoryBookings = filteredCancelled.filter(b => isBeforeToday(b.startTime, now));
    // 修正：已取消tab初始显示最近的5条（优先未来，没有则显示最近历史）
    let visibleCancelledBookings = [];
    if (cancelledFutureBookings.length >= pageSize) {
      visibleCancelledBookings = cancelledFutureBookings.slice(0, pageSize);
    } else if (cancelledFutureBookings.length > 0) {
      // 未来不足5条，补历史
      const need = pageSize - cancelledFutureBookings.length;
      visibleCancelledBookings = cancelledFutureBookings.concat(
        cancelledHistoryBookings.slice(-need)
      );
    } else {
      // 只有历史
      visibleCancelledBookings = cancelledHistoryBookings.slice(-pageSize);
    }
    // 已完成tab分页分组
    const filteredCompleted = completedBookings.filter(filterBooking);
    const completedFutureBookings = filteredCompleted.filter(b => isSameOrAfterToday(b.startTime, now));
    const completedHistoryBookings = filteredCompleted.filter(b => isBeforeToday(b.startTime, now));
    // 修正：已完成tab初始显示最近的历史预约（即最近结束的pageSize条）
    let visibleCompletedBookings = [];
    if (completedHistoryBookings.length > 0) {
      visibleCompletedBookings = completedHistoryBookings.slice(-pageSize);
    } else {
      visibleCompletedBookings = completedFutureBookings.slice(0, pageSize);
    }
    console.log('filterBookings 调试:', {
      filteredAll,
      allFutureBookings,
      allHistoryBookings,
      visibleAllBookings
    });
    console.log('startTime详细调试', filteredAll.map(b => ({
      id: b.id,
      startTime: b.startTime,
      type: typeof b.startTime,
      toString: b.startTime && b.startTime.toString ? b.startTime.toString() : ''
    })));
    this.setData({
      filteredAllBookings: filteredAll,
      filteredUpcomingBookings: upcomingBookings.filter(filterBooking),
      filteredCancelledBookings: cancelledBookings.filter(filterBooking),
      filteredCompletedBookings: completedBookings.filter(filterBooking),
      visibleAllBookings,
      allFutureBookings,
      allHistoryBookings,
      flashIndexes: Array.from({length: visibleAllBookings.length}, (_, i) => i),
      noMoreHistory: allHistoryBookings.length === 0,
      noMoreFuture: allFutureBookings.length <= pageSize,
      visibleCancelledBookings,
      cancelledFutureBookings,
      cancelledHistoryBookings,
      flashIndexesCancelled: Array.from({length: visibleCancelledBookings.length}, (_, i) => i),
      noMoreHistoryCancelled: cancelledHistoryBookings.length === 0,
      noMoreFutureCancelled: cancelledFutureBookings.length <= pageSize,
      visibleCompletedBookings,
      completedFutureBookings,
      completedHistoryBookings,
      flashIndexesCompleted: Array.from({length: visibleCompletedBookings.length}, (_, i) => i),
      noMoreHistoryCompleted: completedHistoryBookings.length === 0,
      noMoreFutureCompleted: completedFutureBookings.length <= pageSize,
    });
    console.log('筛选结果:', {
      activeFilter,
      allCount: filteredAll.length,
      upcomingCount: upcomingBookings.filter(filterBooking).length,
      cancelledCount: cancelledBookings.filter(filterBooking).length,
      completedCount: completedBookings.filter(filterBooking).length
    });
  },

  // scroll-view 事件处理
  onAllScrollToLower() {
    // 加载未来
    if (this.data.isLoadingBottom) return;
    const { visibleAllBookings, allFutureBookings, pageSize } = this.data;
    const last = visibleAllBookings[visibleAllBookings.length-1];
    const lastIdx = allFutureBookings.findIndex(b => b.id === last.id);
    if (lastIdx === -1 || lastIdx === allFutureBookings.length-1) {
      this.setData({ noMoreFuture: true });
      return; // 没有更多
    }
    this.setData({ isLoadingBottom: true, noMoreFuture: false });
    setTimeout(() => {
      const next = allFutureBookings.slice(lastIdx+1, lastIdx+1+pageSize);
      this.setData({
        visibleAllBookings: visibleAllBookings.concat(next),
        isLoadingBottom: false,
        flashIndexes: Array.from({length: next.length}, (_, i) => visibleAllBookings.length + i),
        noMoreFuture: lastIdx+1+pageSize >= allFutureBookings.length
      });
      setTimeout(() => this.setData({ flashIndexes: [] }), 1000);
    }, 600);
  },
  onAllScrollToUpper() {
    // 加载历史
    if (this.data.isLoadingTop) return;
    const { visibleAllBookings, allHistoryBookings, pageSize } = this.data;
    const first = visibleAllBookings[0];
    const firstIdx = allHistoryBookings.findIndex(b => b.id === first.id);
    let startIdx = 0;
    if (firstIdx === -1) {
      startIdx = Math.max(0, allHistoryBookings.length - pageSize);
    } else if (firstIdx === 0) {
      this.setData({ noMoreHistory: true });
      return; // 没有更多
    } else {
      startIdx = Math.max(0, firstIdx - pageSize);
    }
    const endIdx = firstIdx === -1 ? allHistoryBookings.length : firstIdx;
    if (endIdx <= 0) {
      this.setData({ noMoreHistory: true });
      return;
    }
    this.setData({ isLoadingTop: true, noMoreHistory: false });
    setTimeout(() => {
      const prev = allHistoryBookings.slice(startIdx, endIdx);
      this.setData({
        visibleAllBookings: prev.concat(visibleAllBookings),
        isLoadingTop: false,
        flashIndexes: Array.from({length: prev.length}, (_, i) => i),
        noMoreHistory: startIdx === 0
      });
      setTimeout(() => this.setData({ flashIndexes: [] }), 1000);
    }, 600);
  },

  // scroll-view 下拉刷新加载历史卡片
  async onRefresherRefresh() {
    this.setData({ isRefresherTriggered: true, isPullingDown: false });
    // 触发历史卡片加载
    await this.loadMoreHistoryBookings();
    this.setData({ isRefresherTriggered: false });
  },

  // 监听用户下拉动作 - 全部tab
  onRefresherPulling(e) {
    // 当用户开始下拉时显示提示
    if (!this.data.isPullingDown && this.data.noMoreHistory) {
      this.setData({ isPullingDown: true });
    }
  },

  // 监听下拉结束 - 全部tab
  onRefresherAbort() {
    // 当用户松手或取消下拉时隐藏提示
    this.setData({ isPullingDown: false });
  },

  // 封装历史卡片分页加载逻辑，供下拉刷新和上滑触顶复用
  async loadMoreHistoryBookings() {
    if (this.data.isLoadingTop) return;
    const { visibleAllBookings, allHistoryBookings, pageSize } = this.data;
    const first = visibleAllBookings[0];
    const firstIdx = allHistoryBookings.findIndex(b => b.id === first.id);
    let startIdx = 0;
    if (firstIdx === -1) {
      startIdx = Math.max(0, allHistoryBookings.length - pageSize);
    } else if (firstIdx === 0) {
      this.setData({ noMoreHistory: true });
      return;
    } else {
      startIdx = Math.max(0, firstIdx - pageSize);
    }
    const endIdx = firstIdx === -1 ? allHistoryBookings.length : firstIdx;
    if (endIdx <= 0) {
      this.setData({ noMoreHistory: true });
      return;
    }
    this.setData({ isLoadingTop: true, noMoreHistory: false });
    // 保持和原有动画一致
    setTimeout(() => {
      const prev = allHistoryBookings.slice(startIdx, endIdx);
      this.setData({
        visibleAllBookings: prev.concat(visibleAllBookings),
        isLoadingTop: false,
        flashIndexes: Array.from({length: prev.length}, (_, i) => i),
        noMoreHistory: startIdx === 0
      });
      setTimeout(() => this.setData({ flashIndexes: [] }), 1000);
    }, 600);
  },
  // 已取消tab scroll事件
  onCancelledScrollToLower() {
    if (this.data.isLoadingBottomCancelled) return;
    const { visibleCancelledBookings, cancelledFutureBookings, pageSize } = this.data;
    const last = visibleCancelledBookings[visibleCancelledBookings.length-1];
    const lastIdx = cancelledFutureBookings.findIndex(b => b.id === last.id);
    if (lastIdx === -1 || lastIdx === cancelledFutureBookings.length-1) {
      this.setData({ noMoreFutureCancelled: true });
      return;
    }
    this.setData({ isLoadingBottomCancelled: true, noMoreFutureCancelled: false });
    setTimeout(() => {
      const next = cancelledFutureBookings.slice(lastIdx+1, lastIdx+1+pageSize);
      this.setData({
        visibleCancelledBookings: visibleCancelledBookings.concat(next),
        isLoadingBottomCancelled: false,
        flashIndexesCancelled: Array.from({length: next.length}, (_, i) => visibleCancelledBookings.length + i),
        noMoreFutureCancelled: lastIdx+1+pageSize >= cancelledFutureBookings.length
      });
      setTimeout(() => this.setData({ flashIndexesCancelled: [] }), 1000);
    }, 600);
  },
  onCancelledScrollToUpper() {
    if (this.data.isLoadingTopCancelled) return;
    const { visibleCancelledBookings, cancelledHistoryBookings, pageSize } = this.data;
    const first = visibleCancelledBookings[0];
    const firstIdx = cancelledHistoryBookings.findIndex(b => b.id === first.id);
    let startIdx = 0;
    if (firstIdx === -1) {
      startIdx = Math.max(0, cancelledHistoryBookings.length - pageSize);
    } else if (firstIdx === 0) {
      this.setData({ noMoreHistoryCancelled: true });
      return;
    } else {
      startIdx = Math.max(0, firstIdx - pageSize);
    }
    const endIdx = firstIdx === -1 ? cancelledHistoryBookings.length : firstIdx;
    if (endIdx <= 0) {
      this.setData({ noMoreHistoryCancelled: true });
      return;
    }
    this.setData({ isLoadingTopCancelled: true, noMoreHistoryCancelled: false });
    setTimeout(() => {
      const prev = cancelledHistoryBookings.slice(startIdx, endIdx);
      this.setData({
        visibleCancelledBookings: prev.concat(visibleCancelledBookings),
        isLoadingTopCancelled: false,
        flashIndexesCancelled: Array.from({length: prev.length}, (_, i) => i),
        noMoreHistoryCancelled: startIdx === 0
      });
      setTimeout(() => this.setData({ flashIndexesCancelled: [] }), 1000);
    }, 600);
  },
  async onRefresherRefreshCancelled() {
    this.setData({ isRefresherTriggeredCancelled: true, isPullingDownCancelled: false });
    await this.loadMoreHistoryBookingsCancelled();
    this.setData({ isRefresherTriggeredCancelled: false });
  },

  // 监听用户下拉动作 - 已取消tab
  onRefresherPullingCancelled(e) {
    if (!this.data.isPullingDownCancelled && this.data.noMoreHistoryCancelled) {
      this.setData({ isPullingDownCancelled: true });
    }
  },

  // 监听下拉结束 - 已取消tab
  onRefresherAbortCancelled() {
    this.setData({ isPullingDownCancelled: false });
  },
  async loadMoreHistoryBookingsCancelled() {
    if (this.data.isLoadingTopCancelled) return;
    const { visibleCancelledBookings, cancelledHistoryBookings, pageSize } = this.data;
    const first = visibleCancelledBookings[0];
    const firstIdx = cancelledHistoryBookings.findIndex(b => b.id === first.id);
    let startIdx = 0;
    if (firstIdx === -1) {
      startIdx = Math.max(0, cancelledHistoryBookings.length - pageSize);
    } else if (firstIdx === 0) {
      this.setData({ noMoreHistoryCancelled: true });
      return;
    } else {
      startIdx = Math.max(0, firstIdx - pageSize);
    }
    const endIdx = firstIdx === -1 ? cancelledHistoryBookings.length : firstIdx;
    if (endIdx <= 0) {
      this.setData({ noMoreHistoryCancelled: true });
      return;
    }
    this.setData({ isLoadingTopCancelled: true, noMoreHistoryCancelled: false });
    setTimeout(() => {
      const prev = cancelledHistoryBookings.slice(startIdx, endIdx);
      this.setData({
        visibleCancelledBookings: prev.concat(visibleCancelledBookings),
        isLoadingTopCancelled: false,
        flashIndexesCancelled: Array.from({length: prev.length}, (_, i) => i),
        noMoreHistoryCancelled: startIdx === 0
      });
      setTimeout(() => this.setData({ flashIndexesCancelled: [] }), 1000);
    }, 600);
  },
  // 已完成tab scroll事件
  onCompletedScrollToLower() {
    if (this.data.isLoadingBottomCompleted) return;
    const { visibleCompletedBookings, completedFutureBookings, pageSize } = this.data;
    const last = visibleCompletedBookings[visibleCompletedBookings.length-1];
    const lastIdx = completedFutureBookings.findIndex(b => b.id === last.id);
    if (lastIdx === -1 || lastIdx === completedFutureBookings.length-1) {
      this.setData({ noMoreFutureCompleted: true });
      return;
    }
    this.setData({ isLoadingBottomCompleted: true, noMoreFutureCompleted: false });
    setTimeout(() => {
      const next = completedFutureBookings.slice(lastIdx+1, lastIdx+1+pageSize);
      this.setData({
        visibleCompletedBookings: visibleCompletedBookings.concat(next),
        isLoadingBottomCompleted: false,
        flashIndexesCompleted: Array.from({length: next.length}, (_, i) => visibleCompletedBookings.length + i),
        noMoreFutureCompleted: lastIdx+1+pageSize >= completedFutureBookings.length
      });
      setTimeout(() => this.setData({ flashIndexesCompleted: [] }), 1000);
    }, 600);
  },

  /**
   * onCompletedScrollToUpper: 已完成Tab向上滚动事件处理
   *
   * 功能说明：
   * 用户在已完成Tab中向上滚动到顶部时，加载更多历史数据
   * 实现双向分页中的"向上加载历史数据"功能
   *
   * 实现逻辑：
   * 与其他Tab的向上滚动逻辑相同，都是基于相同的分页算法
   * 通过计算当前显示数据在历史数据中的位置，确定需要加载的数据范围
   */
  onCompletedScrollToUpper() {
    if (this.data.isLoadingTopCompleted) return;
    const { visibleCompletedBookings, completedHistoryBookings, pageSize } = this.data;
    const first = visibleCompletedBookings[0];
    const firstIdx = completedHistoryBookings.findIndex(b => b.id === first.id);
    let startIdx = 0;
    if (firstIdx === -1) {
      startIdx = Math.max(0, completedHistoryBookings.length - pageSize);
    } else if (firstIdx === 0) {
      this.setData({ noMoreHistoryCompleted: true });
      return;
    } else {
      startIdx = Math.max(0, firstIdx - pageSize);
    }
    const endIdx = firstIdx === -1 ? completedHistoryBookings.length : firstIdx;
    if (endIdx <= 0) {
      this.setData({ noMoreHistoryCompleted: true });
      return;
    }
    this.setData({ isLoadingTopCompleted: true, noMoreHistoryCompleted: false });
    setTimeout(() => {
      const prev = completedHistoryBookings.slice(startIdx, endIdx);
      this.setData({
        visibleCompletedBookings: prev.concat(visibleCompletedBookings),
        isLoadingTopCompleted: false,
        flashIndexesCompleted: Array.from({length: prev.length}, (_, i) => i),
        noMoreHistoryCompleted: startIdx === 0
      });
      setTimeout(() => this.setData({ flashIndexesCompleted: [] }), 1000);
    }, 600);
  },

  /**
   * onRefresherRefreshCompleted: 已完成Tab下拉刷新事件处理
   *
   * 功能说明：
   * 用户在已完成Tab中下拉刷新时，加载更多历史数据
   * 提供手动刷新的交互方式
   */
  async onRefresherRefreshCompleted() {
    this.setData({ isRefresherTriggeredCompleted: true, isPullingDownCompleted: false });
    await this.loadMoreHistoryBookingsCompleted();
    this.setData({ isRefresherTriggeredCompleted: false });
  },

  // 监听用户下拉动作 - 已完成tab
  onRefresherPullingCompleted(e) {
    if (!this.data.isPullingDownCompleted && this.data.noMoreHistoryCompleted) {
      this.setData({ isPullingDownCompleted: true });
    }
  },

  // 监听下拉结束 - 已完成tab
  onRefresherAbortCompleted() {
    this.setData({ isPullingDownCompleted: false });
  },

  /**
   * loadMoreHistoryBookingsCompleted: 已完成Tab加载更多历史数据
   *
   * 功能说明：
   * 封装的历史数据加载逻辑，供下拉刷新和向上滚动复用
   * 实现代码复用，避免重复的分页逻辑
   */
  async loadMoreHistoryBookingsCompleted() {
    if (this.data.isLoadingTopCompleted) return;
    const { visibleCompletedBookings, completedHistoryBookings, pageSize } = this.data;
    const first = visibleCompletedBookings[0];
    const firstIdx = completedHistoryBookings.findIndex(b => b.id === first.id);
    let startIdx = 0;
    if (firstIdx === -1) {
      startIdx = Math.max(0, completedHistoryBookings.length - pageSize);
    } else if (firstIdx === 0) {
      this.setData({ noMoreHistoryCompleted: true });
      return;
    } else {
      startIdx = Math.max(0, firstIdx - pageSize);
    }
    const endIdx = firstIdx === -1 ? completedHistoryBookings.length : firstIdx;
    if (endIdx <= 0) {
      this.setData({ noMoreHistoryCompleted: true });
      return;
    }
    this.setData({ isLoadingTopCompleted: true, noMoreHistoryCompleted: false });
    setTimeout(() => {
      const prev = completedHistoryBookings.slice(startIdx, endIdx);
      this.setData({
        visibleCompletedBookings: prev.concat(visibleCompletedBookings),
        isLoadingTopCompleted: false,
        flashIndexesCompleted: Array.from({length: prev.length}, (_, i) => i),
        noMoreHistoryCompleted: startIdx === 0
      });
      setTimeout(() => this.setData({ flashIndexesCompleted: [] }), 1000);
    }, 600);
  },

  /**
   * 分页方法总结
   *
   * 这个文件包含了大量相似的分页方法，它们都遵循相同的设计模式：
   *
   * 1. 方法命名规律：
   *    - onXxxScrollToLower: 向下滚动加载更多未来数据
   *    - onXxxScrollToUpper: 向上滚动加载更多历史数据
   *    - onRefresherRefreshXxx: 下拉刷新事件处理
   *    - loadMoreHistoryBookingsXxx: 封装的历史数据加载逻辑
   *
   * 2. 核心算法：
   *    - 双向分页：支持向前和向后加载数据
   *    - 索引计算：通过findIndex找到当前数据在完整数据中的位置
   *    - 范围计算：计算需要加载的数据范围（startIdx到endIdx）
   *    - 边界处理：处理没有更多数据的情况
   *
   * 3. 用户体验：
   *    - 加载动画：显示loading状态，防止重复操作
   *    - 闪烁效果：新加载的数据会有闪烁提示
   *    - 平滑过渡：使用setTimeout实现平滑的加载效果
   *
   * 4. 性能优化：
   *    - 按需加载：只加载用户需要看到的数据
   *    - 防重复：通过loading状态防止重复加载
   *    - 内存管理：合理控制内存中的数据量
   *
   * 这种设计模式在处理大量数据的列表页面中非常常见，
   * 是移动端应用性能优化的重要技术。
   */
});

/**
 * 文件总结：my-bookings.js
 *
 * 这个文件实现了一个功能极其复杂的预约管理页面，是整个项目中最复杂的页面之一。
 *
 * 主要特点：
 *
 * 1. 复杂的数据架构：
 *    - 多层数据结构：原始数据 → 分类数据 → 筛选数据 → 显示数据
 *    - 多维度分类：按状态（全部、已取消、已完成）和时间（未来、历史）双重分类
 *    - 实时状态计算：根据当前时间自动计算预约的实际状态
 *    - 关联数据查询：预约、课程、用户信息的多表关联
 *
 * 2. 高级分页系统：
 *    - 双向分页：支持向上加载历史数据、向下加载未来数据
 *    - 多Tab独立分页：每个Tab都有独立的分页状态和数据管理
 *    - 性能优化：只加载可见区域的数据，支持大量预约记录
 *    - 用户体验：平滑的加载动画和闪烁提示效果
 *
 * 3. 复杂的业务逻辑：
 *    - 状态自动更新：根据时间自动更新预约状态
 *    - 权限控制：根据时间限制判断是否可以取消预约
 *    - 数据同步：预约操作后实时更新列表状态
 *    - 错误处理：完善的异常处理和用户提示
 *
 * 4. 用户交互优化：
 *    - Tab切换：流畅的Tab切换和数据筛选
 *    - 下拉刷新：支持下拉刷新加载历史数据
 *    - 上拉加载：支持上拉加载更多未来数据
 *    - 视觉反馈：新加载数据的闪烁提示效果
 *
 * 5. 性能优化策略：
 *    - 批量查询：使用批量查询减少网络请求
 *    - 数据缓存：使用Map结构快速查找关联数据
 *    - 懒加载：按需加载数据，避免一次性加载过多内容
 *    - 防抖处理：避免频繁的数据更新操作
 *
 * 技术亮点：
 *
 * 1. 数据处理：
 *    - 使用Array.reduce()构建映射表
 *    - 使用Set进行数据去重
 *    - 使用扩展运算符进行数组操作
 *    - 复杂的数组分割和合并逻辑
 *
 * 2. 异步处理：
 *    - Promise.all并行查询优化
 *    - async/await异步流程控制
 *    - setTimeout实现动画效果
 *    - 错误处理和重试机制
 *
 * 3. 状态管理：
 *    - 多维度状态跟踪
 *    - 状态自动计算和更新
 *    - UI状态与数据状态的同步
 *    - 复杂的条件判断逻辑
 *
 * 与您熟悉的技术对比：
 *
 * - 数据架构：类似于Entity Framework的复杂查询和关联
 * - 分页系统：类似于ASP.NET的分页控件，但更复杂
 * - 状态管理：类似于WPF的MVVM模式，但状态更复杂
 * - 异步处理：类似于C#的async/await模式
 * - 性能优化：类似于数据库的索引和查询优化
 *
 * 学习价值：
 *
 * 这个文件展示了如何在前端处理复杂的业务逻辑：
 * 1. 复杂数据结构的设计和管理
 * 2. 高性能分页系统的实现
 * 3. 多维度数据筛选和状态管理
 * 4. 用户体验和性能的平衡
 * 5. 错误处理和边界情况的考虑
 *
 * 这种复杂度在企业级应用中很常见，是前端开发能力的重要体现。
 */